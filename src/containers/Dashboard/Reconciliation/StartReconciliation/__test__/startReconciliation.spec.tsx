import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import StartReconciliation from '../index';

const MockedStartReconciliation = () => {
  return (
    <MockIndex>
      <StartReconciliation />
    </MockIndex>
  );
};

describe('StartReconciliation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be accessible', async () => {
    const { container } = render(<MockedStartReconciliation />);

    // Wait for component to fully render
    await waitFor(() => {
      expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct content', async () => {
    render(<MockedStartReconciliation />);

    expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    expect(screen.getByText('Fill the form below to start reconciliation process.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });

  it('should render form fields correctly', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Payment Type')).toBeInTheDocument();
    expect(screen.getByText('Report Date Range')).toBeInTheDocument();
    expect(screen.getByText('Upload record for reconciliation')).toBeInTheDocument();
  });

  it('should load processor dropdown', async () => {
    render(<MockedStartReconciliation />);

    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });
  });

  it('should handle form interactions', async () => {
    render(<MockedStartReconciliation />);

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Select Processor')).toBeInTheDocument();
    });

    // Test that payment type defaults to 'Pay-ins'
    expect(screen.getByText('Pay-ins')).toBeInTheDocument();
  });

  it('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<MockedStartReconciliation />);

    const fileInput = screen.getByLabelText(/upload record for reconciliation/i);
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);
    expect((fileInput as HTMLInputElement).files?.[0]).toBe(file);
  });

  it('should validate form completion', () => {
    render(<MockedStartReconciliation />);

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).toBeDisabled();
  });
});
