import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { useReconciliationHistory } from '../hooks/useReconciliationHistory';
import ReconciliationHistory from '../ReconciliationHistory';

// Mock the custom hook
vi.mock('../hooks/useReconciliationHistory', () => ({
  useReconciliationHistory: vi.fn()
}));

describe('ReconciliationHistory', () => {
  const mockHistoryData = [
    {
      id: '1',
      name: 'January Reconciliation',
      status: 'completed',
      dateCreated: '2023-01-15T10:30:00Z',
      createdBy: '<PERSON>',
      lastModified: '2023-01-16T14:20:00Z'
    },
    {
      id: '2',
      name: 'February Reconciliation',
      status: 'pending',
      dateCreated: '2023-02-10T08:15:00Z',
      createdBy: '<PERSON>',
      lastModified: '2023-02-10T08:15:00Z'
    }
  ];

  const mockUseReconciliationHistory = {
    reconciliationHistory: mockHistoryData,
    isLoading: false,
    error: null,
    fetchReconciliationHistory: vi.fn(),
    deleteReconciliationRecord: vi.fn(),
    currentPage: 1,
    totalPages: 5,
    handlePageChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useReconciliationHistory as any).mockReturnValue(mockUseReconciliationHistory);
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter initialEntries={['/reconciliation/history']}>
        <Routes>
          <Route path="/reconciliation/history" element={<ReconciliationHistory />} />
          <Route path="/reconciliation/:id" element={<div>Reconciliation Detail</div>} />
        </Routes>
      </MemoryRouter>
    );
  };

  it('should be accessible', async () => {
    const { container } = renderComponent();
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the reconciliation history table', () => {
    renderComponent();

    expect(screen.getByText('Reconciliation History')).toBeInTheDocument();
    expect(screen.getByText('January Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('February Reconciliation')).toBeInTheDocument();
  });

  it('should fetch reconciliation history on mount', () => {
    renderComponent();
    expect(mockUseReconciliationHistory.fetchReconciliationHistory).toHaveBeenCalledTimes(1);
  });

  it('should display loading state when data is being fetched', () => {
    (useReconciliationHistory as any).mockReturnValue({
      ...mockUseReconciliationHistory,
      isLoading: true,
      reconciliationHistory: []
    });

    renderComponent();
    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
    expect(screen.queryByText('January Reconciliation')).not.toBeInTheDocument();
  });

  it('should display error message when fetching fails', () => {
    (useReconciliationHistory as any).mockReturnValue({
      ...mockUseReconciliationHistory,
      error: 'Failed to fetch reconciliation history',
      reconciliationHistory: []
    });

    renderComponent();
    expect(screen.getByText('Failed to fetch reconciliation history')).toBeInTheDocument();
  });

  it('should display no data message when no records are available', () => {
    (useReconciliationHistory as any).mockReturnValue({
      ...mockUseReconciliationHistory,
      reconciliationHistory: []
    });

    renderComponent();
    expect(screen.getByText('No reconciliation history found')).toBeInTheDocument();
  });

  it('should navigate to detail page when clicking on a record', async () => {
    renderComponent();

    const firstRecord = screen.getByText('January Reconciliation');
    await userEvent.click(firstRecord);

    expect(screen.getByText('Reconciliation Detail')).toBeInTheDocument();
  });

  it('should delete a record when delete button is clicked and confirmed', async () => {
    renderComponent();

    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    await userEvent.click(deleteButtons[0]);

    // Confirm deletion in modal
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    await userEvent.click(confirmButton);

    expect(mockUseReconciliationHistory.deleteReconciliationRecord).toHaveBeenCalledWith('1');
  });

  it('should not delete record when deletion is canceled', async () => {
    renderComponent();

    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    await userEvent.click(deleteButtons[0]);

    // Cancel deletion in modal
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await userEvent.click(cancelButton);

    expect(mockUseReconciliationHistory.deleteReconciliationRecord).not.toHaveBeenCalled();
  });

  it('should handle pagination correctly', async () => {
    renderComponent();

    const nextPageButton = screen.getByRole('button', { name: /next page/i });
    await userEvent.click(nextPageButton);

    expect(mockUseReconciliationHistory.handlePageChange).toHaveBeenCalledWith(2);
  });

  it('should display correct pagination information', () => {
    renderComponent();

    expect(screen.getByText('Page 1 of 5')).toBeInTheDocument();
  });

  it('should format dates correctly in the table', () => {
    renderComponent();

    expect(screen.getByText('Jan 15, 2023')).toBeInTheDocument(); // Formatted date
  });

  it('should display status with appropriate styling', () => {
    renderComponent();

    const completedStatus = screen.getByText('completed');
    const pendingStatus = screen.getByText('pending');

    expect(completedStatus).toHaveClass('status-completed');
    expect(pendingStatus).toHaveClass('status-pending');
  });

  it('should refresh data when refresh button is clicked', async () => {
    renderComponent();

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    await userEvent.click(refreshButton);

    expect(mockUseReconciliationHistory.fetchReconciliationHistory).toHaveBeenCalledTimes(2);
  });
});
