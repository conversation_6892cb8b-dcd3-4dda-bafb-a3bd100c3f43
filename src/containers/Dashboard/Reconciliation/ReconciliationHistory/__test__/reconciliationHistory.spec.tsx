import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import ReconciliationHistory from '../index';

const MockedReconciliationHistory = () => {
  return (
    <MockIndex>
      <ReconciliationHistory />
    </MockIndex>
  );
};

describe('ReconciliationHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be accessible', async () => {
    const { container } = render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });

    const results = await axe(container, {
      rules: {
        // Exclude any known accessibility issues if needed
        'color-contrast': { enabled: false }
      }
    });
    expect(results).toHaveNoViolations();
  });

  it('should render the reconciliation history page with correct content', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        'This application enables you to check and match transaction details between Kora and Processors. It enables you identify and rectify errors or unpaid transactions, ensuring that the records of both parties are accurate.'
      )
    ).toBeInTheDocument();
  });

  it('should render the reconciliation history table with data', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    expect(screen.getByText('February 2024 Monnify Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('March 2024 Flutterwave Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('April 2024 Paystack Reconciliation')).toBeInTheDocument();
  });

  it('should display correct status indicators', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Successful')).toBeInTheDocument();
    });

    expect(screen.getByText('Processing')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('should display formatted dates correctly', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('15 Jan 2024')).toBeInTheDocument();
    });

    // Also check for the time component
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
  });

  it('should show download action for completed reconciliations', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    // The successful reconciliation should have a download action
    const downloadActions = screen.getAllByTestId('action');
    expect(downloadActions).toHaveLength(1); // Only successful reconciliation has result_file_id
  });

  it('should handle table row interactions', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    // Test that table rows are clickable
    const firstRow = screen.getByTestId('table-row-0-column-1');
    expect(firstRow).toBeInTheDocument();
    expect(firstRow).toHaveAttribute('role', 'button');
    expect(firstRow).toHaveAttribute('tabindex', '0');
  });

  it('should display table headers correctly', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Status')).toBeInTheDocument();
    });

    expect(screen.getByText('Reconciled Report')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('should show loading state while fetching data', async () => {
    render(<MockedReconciliationHistory />);

    // The component should show loading initially
    // Since we're using react-query, the loading state might be brief
    // We'll just verify the component renders without errors
    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });
  });

  it('should display pagination information', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    // Check if pagination controls are present (they should be rendered by the Table component)
    // The exact pagination text depends on the Table component implementation
    const tableElement = document.querySelector('.table-responsive');
    expect(tableElement).toBeInTheDocument();
  });
});
